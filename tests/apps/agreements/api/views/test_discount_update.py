import json
from decimal import Decimal
from unittest.mock import Mock

import pytest
from django.contrib.auth.models import AnonymousUser
from django.test import RequestFactory
from django.urls import reverse_lazy
from rest_framework import status
from rest_framework.response import Response
from rest_framework.test import force_authenticate

from nga.apps.agreements.api.serializers.discount_detail import (
    CommitmentDistributionParameterSerializer,
    DiscountQualifyingRuleSerializer,
)
from nga.apps.agreements.api.views.discount_update import DiscountPatchUpdateAPIView
from nga.apps.agreements.commands import UpdateDiscountCommand
from nga.apps.agreements.domain.dto import DiscountParameterDTO
from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountQualifyingBasisEnum,
)
from nga.apps.users.models import User
from nga.core.enums import ServiceTypeEnum
from nga.core.types import Month
from tests.apps.agreements.fakes import (
    FakeDiscountSchemaMapper,
    InMemoryBudgetAgreementRepository,
    InMemoryDiscountRepository,
)
from tests.core.fakes import InMemoryMediator
from tests.factories.agreements import BudgetAgreementFactory
from tests.factories.agreements.api_schemas import DiscountSchemaFactory
from tests.factories.agreements.domain import DiscountFactory


@pytest.mark.django_db
class TestDiscountPatchUpdateAPIView:
    view_class = DiscountPatchUpdateAPIView
    url_name = "discount"

    def test_authentication_required(self, rf: RequestFactory):
        url = reverse_lazy(self.url_name, kwargs={"id": "2", "discount_id": "12"})
        request = rf.patch(url)
        request.user = AnonymousUser()

        response = self.view_class.as_view()(request)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_200_ok_with_empty_body(self, staff_user, rf, in_memory_uow, override_deps):
        budget_agreement = BudgetAgreementFactory()
        budget_agreement_repository = InMemoryBudgetAgreementRepository(agreements=[budget_agreement])

        discount = DiscountFactory(agreement_id=budget_agreement.agreement_id)
        schema = DiscountSchemaFactory()

        mediator = InMemoryMediator(response=discount)

        with override_deps(
            uow=in_memory_uow,
            mediator=mediator,
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=InMemoryDiscountRepository(discounts=[discount]),
            discount_schema_mapper=FakeDiscountSchemaMapper([schema]),
        ):
            response = self.patch_discount(
                staff_user,
                rf,
                data={},
                discount_id=discount.id,
                agreement_id=budget_agreement.id,
            )

        assert response.status_code == status.HTTP_200_OK

        expected_data = self.view_class.response_serializer_class(schema).data

        assert json.loads(response.rendered_content) == json.loads(json.dumps(expected_data))

    def test_patch_discount_only(self, staff_user, rf, in_memory_uow, override_deps):
        budget_agreement = BudgetAgreementFactory()
        budget_agreement_repository = InMemoryBudgetAgreementRepository(agreements=[budget_agreement])

        discount = DiscountFactory()

        data = {
            "start_date": "2023-01",
            "home_operators": [34, 341],
            "currency_code": "EUR",
            "qualifying_rule": {
                "direction": DiscountDirectionEnum.OUTBOUND.name,
                "service_types": [ServiceTypeEnum.SMS_MO.name],
                "basis": DiscountQualifyingBasisEnum.AVERAGE_MONTHLY_USAGE_PER_IMSI.name,
                "lower_bound": "12",
            },
            "commitment_distribution_parameters": [
                {
                    "home_operators": [34],
                    "partner_operators": discount.partner_operators,
                    "charge": "34.89",
                }
            ],
            "inbound_market_share": "78.95",
        }

        mediator = InMemoryMediator(response=discount)

        with override_deps(
            uow=in_memory_uow,
            mediator=mediator,
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=InMemoryDiscountRepository(discounts=[discount]),
            discount_schema_mapper=FakeDiscountSchemaMapper([DiscountSchemaFactory()]),
        ):
            response = self.patch_discount(
                staff_user,
                rf,
                data=data,
                discount_id=discount.id,
                agreement_id=budget_agreement.id,
            )

        assert response.status_code == status.HTTP_200_OK

        update_cmd = mediator.get_message_by_type(UpdateDiscountCommand)

        qualifying_rule_serializer = DiscountQualifyingRuleSerializer(data=data["qualifying_rule"])
        qualifying_rule_serializer.is_valid()
        qualifying_rule = qualifying_rule_serializer.create(qualifying_rule_serializer.validated_data)

        cd_params_serializer = CommitmentDistributionParameterSerializer(
            many=True, data=data["commitment_distribution_parameters"]
        )
        cd_params_serializer.is_valid()
        cd_params = cd_params_serializer.create(cd_params_serializer.validated_data)

        assert update_cmd.discount == discount
        assert update_cmd.budget_agreement == budget_agreement
        assert update_cmd.data["home_operators"] == data["home_operators"]
        assert update_cmd.data["start_date"] == Month.create_from_year_month(2023, 1)
        assert update_cmd.data["currency_code"] == data["currency_code"]
        assert update_cmd.data["inbound_market_share"] == Decimal(data["inbound_market_share"])
        assert update_cmd.data["qualifying_rule"] == qualifying_rule
        assert update_cmd.data["commitment_distribution_parameters"] == cd_params
        assert update_cmd.parameters_dtos == tuple()

    def test_patch_parameters_only(self, staff_user, rf, in_memory_uow, override_deps):
        budget_agreement = BudgetAgreementFactory()
        budget_agreement_repository = InMemoryBudgetAgreementRepository(agreements=[budget_agreement])

        discount = DiscountFactory()

        parameters_data = {
            "calculation_type": DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
            "basis": DiscountBasisEnum.VALUE.name,
            "basis_value": "45",
            "balancing": DiscountBalancingEnum.BALANCED.name,
            "bound_type": DiscountBoundTypeEnum.VOLUME.name,
            "lower_bound": "68",
            "upper_bound": "89",
        }

        mediator = InMemoryMediator(response=discount)

        with override_deps(
            uow=in_memory_uow,
            mediator=mediator,
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=InMemoryDiscountRepository(discounts=[discount]),
            discount_schema_mapper=FakeDiscountSchemaMapper([DiscountSchemaFactory()]),
        ):
            response = self.patch_discount(
                staff_user,
                rf,
                data={"parameters": [parameters_data]},
                discount_id=discount.id,
                agreement_id=budget_agreement.id,
            )

        assert response.status_code == status.HTTP_200_OK

        update_cmd = mediator.get_message_by_type(UpdateDiscountCommand)
        assert update_cmd.discount == discount
        assert update_cmd.data == {}

        assert len(update_cmd.parameters_dtos) == 1

        dto = update_cmd.parameters_dtos[0]

        assert isinstance(dto, DiscountParameterDTO)
        assert dto.calculation_type == DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE
        assert dto.basis == DiscountBasisEnum.VALUE
        assert dto.basis_value == Decimal("45")
        assert dto.balancing == DiscountBalancingEnum.BALANCED
        assert dto.bound_type == DiscountBoundTypeEnum.VOLUME
        assert dto.lower_bound == Decimal("68")
        assert dto.upper_bound == Decimal("89")
        assert dto.toll_rate is None
        assert dto.airtime_rate is None
        assert dto.fair_usage_rate is None
        assert dto.fair_usage_threshold is None

    def test_patch_financial_threshold_fields(self, staff_user, rf, in_memory_uow, override_deps):
        budget_agreement = BudgetAgreementFactory()
        budget_agreement_repository = InMemoryBudgetAgreementRepository(agreements=[budget_agreement])

        discount = DiscountFactory()

        data = {
            "financial_threshold": "1000.50",
            "above_financial_threshold_rate": "0.15",
        }

        mediator = InMemoryMediator(response=discount)

        with override_deps(
            uow=in_memory_uow,
            mediator=mediator,
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=InMemoryDiscountRepository(discounts=[discount]),
            discount_schema_mapper=FakeDiscountSchemaMapper([DiscountSchemaFactory()]),
        ):
            response = self.patch_discount(
                staff_user,
                rf,
                data=data,
                discount_id=discount.id,
                agreement_id=budget_agreement.id,
            )

        assert response.status_code == status.HTTP_200_OK

        update_cmd = mediator.get_message_by_type(UpdateDiscountCommand)
        assert update_cmd.discount == discount
        assert update_cmd.data["financial_threshold"] == Decimal("1000.50")
        assert update_cmd.data["above_financial_threshold_rate"] == Decimal("0.15")
        assert update_cmd.parameters_dtos == tuple()

    def test_404_when_discount_does_not_exist(self, staff_user, rf, in_memory_uow, in_memory_mediator, override_deps):
        budget_agreement = BudgetAgreementFactory()
        budget_agreement_repository = InMemoryBudgetAgreementRepository(agreements=[budget_agreement])

        with override_deps(
            uow=in_memory_uow,
            mediator=in_memory_mediator,
            discount_repository=InMemoryDiscountRepository(discounts=[]),
            budget_agreement_repository=budget_agreement_repository,
        ):
            response = self.patch_discount(staff_user, rf, data={}, discount_id=101, agreement_id=budget_agreement.id)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_500_when_expected_exception_is_raised(self, staff_user, rf, in_memory_uow, override_deps):
        discount = DiscountFactory()

        budget_agreement = BudgetAgreementFactory()
        budget_agreement_repository = InMemoryBudgetAgreementRepository(agreements=[budget_agreement])

        def process_message(msg):
            if isinstance(msg, UpdateDiscountCommand):
                raise DiscountValidationError("Fake error")

        mediator = Mock()
        mediator.send.side_effect = process_message

        with override_deps(
            uow=in_memory_uow,
            mediator=mediator,
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=InMemoryDiscountRepository(discounts=[discount]),
        ):
            response = self.patch_discount(
                staff_user,
                rf,
                data={},
                discount_id=discount.id,
                agreement_id=budget_agreement.id,
            )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.data["detail"] == "Fake error"

    def test_500_when_non_expected_exception_is_raised(self, staff_user, rf, in_memory_uow, override_deps):
        discount = DiscountFactory()

        budget_agreement = BudgetAgreementFactory()
        budget_agreement_repository = InMemoryBudgetAgreementRepository(agreements=[budget_agreement])

        def process_message(msg):
            if isinstance(msg, UpdateDiscountCommand):
                raise Exception("Fake unexpected error")

        mediator = Mock()
        mediator.send.side_effect = process_message

        with override_deps(
            uow=in_memory_uow,
            mediator=mediator,
            discount_repository=InMemoryDiscountRepository(discounts=[discount]),
            budget_agreement_repository=budget_agreement_repository,
        ):
            response = self.patch_discount(
                staff_user,
                rf,
                data={},
                discount_id=discount.id,
                agreement_id=budget_agreement.id,
            )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.data["detail"] == "Fake unexpected error"

    def patch_discount(
        self,
        user: User,
        rf: RequestFactory,
        data: dict,
        discount_id: int,
        agreement_id: int,
    ) -> Response:
        url = reverse_lazy(self.url_name, kwargs={"id": agreement_id, "discount_id": discount_id})
        request = rf.patch(url, data=data, content_type="application/json")
        force_authenticate(request, user)
        response = self.view_class.as_view()(request, id=agreement_id, discount_id=discount_id)

        return response
