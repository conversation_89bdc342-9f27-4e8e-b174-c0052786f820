import pytest

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.specifications.discount_setup import SREDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.base import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    credit_note_eoa_settlement_method,
    service_types_with_combo,
)
from tests.factories.agreements.domain import (
    DiscountFactory,
    SREDiscountFactory,
    SREDiscountParameterFactory,
    SteppedTieredDiscountParameterFactory,
)


class TestSREDiscountSetupSpecification:
    @classmethod
    def verify_discount(cls, discount):
        SREDiscountSetupSpecification().verify(discount)

    def test_when_ok(self):
        sre_discount = SREDiscountFactory()

        self.verify_discount(sre_discount)

    def test_violates_by_number_of_parameters(self):
        discount = DiscountFactory(parameters=[SREDiscountParameterFactory(), SREDiscountParameterFactory()])

        with pytest.raises(DiscountValidationError) as exc_info:
            self.verify_discount(discount)

        assert "1" in exc_info.value.message

    def test_violates_by_calculation_type(self):
        discount = SREDiscountFactory(parameters=[SteppedTieredDiscountParameterFactory()])

        with pytest.raises(DiscountValidationError) as exc_info:
            self.verify_discount(discount)

        assert "calculation type" in exc_info.value.message

    def test_discount_validators(self):
        assert SREDiscountSetupSpecification.discount_validators == (
            service_types_with_combo,
            credit_note_eoa_settlement_method,
        )

    def test_discount_parameter_validators(self):
        assert (
            SREDiscountSetupSpecification.discount_parameter_validators
            == AbstractDiscountSetupSpecification.discount_parameter_validators
        )
