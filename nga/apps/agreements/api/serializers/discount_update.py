from rest_framework import serializers

from nga.apps.agreements.api.serializers.discount_create import DiscountParameterCreateSerializer
from nga.apps.agreements.api.serializers.discount_detail import (
    CommitmentDistributionParameterSerializer,
    DiscountQualifyingRuleSerializer,
)
from nga.apps.agreements.api.serializers.fields import BoundDecimalField, DiscountDecimalField
from nga.apps.agreements.enums import (
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.common.serializer_fields import <PERSON><PERSON>rencyCodeField, EnumChoiceField, MonthField
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum


class BaseDiscountUpdateSerializer(serializers.Serializer):
    home_operators = serializers.ListField(child=serializers.IntegerField(), required=False)
    partner_operators = serializers.ListField(child=serializers.IntegerField(), required=False)

    direction = EnumChoiceField(enum_class=DiscountDirectionEnum, required=False)

    service_types = serializers.ListField(child=EnumChoiceField(enum_class=ServiceTypeEnum), required=False)

    start_date = MonthField(required=False)
    end_date = MonthField(required=False)

    model_type = EnumChoiceField(enum_class=DiscountModelTypeEnum, required=False)

    currency_code = CurrencyCodeField(required=False)

    tax_type = EnumChoiceField(enum_class=TaxTypeEnum, required=False)
    volume_type = EnumChoiceField(enum_class=VolumeTypeEnum, required=False)
    settlement_method = EnumChoiceField(enum_class=DiscountSettlementMethodEnum, required=False)

    call_destinations = serializers.ListField(
        child=EnumChoiceField(enum_class=CallDestinationEnum),
        required=False,
        allow_empty=True,
        allow_null=True,
    )

    called_countries = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        allow_empty=True,
        allow_null=True,
    )

    traffic_segments = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        allow_empty=True,
        allow_null=True,
    )

    imsi_count_type = EnumChoiceField(
        enum_class=IMSICountTypeEnum,
        required=False,
        allow_null=True,
        default=None,
    )

    qualifying_rule = DiscountQualifyingRuleSerializer(
        required=False,
        allow_null=True,
        default=None,
    )

    inbound_market_share = BoundDecimalField(required=False, allow_null=True, default=None)

    # just for marker that parameters should be changed
    parameters = DiscountParameterCreateSerializer(many=True, allow_null=True)


class DiscountUpdateSerializer(BaseDiscountUpdateSerializer):
    commitment_distribution_parameters = serializers.ListField(
        child=CommitmentDistributionParameterSerializer(),
        required=False,
        allow_empty=True,
        allow_null=True,
        default=None,
    )

    financial_threshold = BoundDecimalField(
        required=False,
        allow_null=True,
        default=None,
    )

    above_financial_threshold_rate = DiscountDecimalField(
        required=False,
        allow_null=True,
        default=None,
    )


class SubDiscountUpdateSerializer(BaseDiscountUpdateSerializer):
    above_commitment_rate = DiscountDecimalField(required=False, allow_null=True, default=None)
    above_financial_threshold_rate = DiscountDecimalField(required=False, allow_null=True, default=None)
